package dev.pigmomo.yhkit2025.api.service

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestHelper
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.SignType
import dev.pigmomo.yhkit2025.api.encryption.EncryptionUtil
import dev.pigmomo.yhkit2025.api.model.cart.CartItem
import dev.pigmomo.yhkit2025.api.model.order.CouponsWidgetData
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceData
import dev.pigmomo.yhkit2025.api.model.order.RedPacketWidgetData
import dev.pigmomo.yhkit2025.api.model.order.WidgetHelper
import dev.pigmomo.yhkit2025.api.model.user.AddressItem
import dev.pigmomo.yhkit2025.api.model.user.AddressLocation
import dev.pigmomo.yhkit2025.api.utils.SignUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.net.URLEncoder
import java.util.UUID
import kotlin.collections.get

/**
 * 订单服务类
 * 提供订单相关的API调用方法
 */
class OrderService(requestHelper: RequestHelper) : BaseService(requestHelper) {

    private val tag = "OrderService"

    private val serviceType = requestHelper.getServiceType()

    /**
     * 获取订单列表
     * @param filter 过滤条件
     * @param orderType 订单类型
     * @param lastOrderId 最后一个订单ID
     * @param cashierSwitch 收银开关
     * @param showAll 显示所有
     * @return 订单列表响应结果
     */
    suspend fun getOrderList(
        filter: String = "1",
        orderType: String = "1",
        lastOrderId: String = "0",
        cashierSwitch: String = "1",
        showAll: String = "1"
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "getOrderList: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建业务参数
                val businessParams = buildAppBusinessParams()
                businessParams["filter"] = filter
                businessParams["lastorderid"] = lastOrderId
                businessParams["shopid"] = shopId
                businessParams["ordertype"] = orderType
                businessParams["sellerid"] = sellerId
                businessParams["cashierswitch"] = cashierSwitch
                businessParams["showall"] = showAll

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(RequestConfig.Path.ORDER_LIST_PATH, businessParams, commonParams)

                // 构建需要签名的URL
                val needSignStr = urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                    .replace("=", "").replace("&", "")

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "getOrderList: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getOrderList: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, false, false)

                requestHelper.get(fullUrl, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "getOrderList: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["filter"] = filter
                businessParams["lastorderid"] = lastOrderId
                businessParams["shopid"] = shopId
                businessParams["ordertype"] = orderType
                businessParams["sellerid"] = sellerId
                businessParams["cashierswitch"] = cashierSwitch
                businessParams["showall"] = showAll
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.ORDER_LIST_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    "",
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getOrderList: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.get(fullUrl, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 获取订单详情
     * @param orderId 订单ID
     * @return 订单详情响应结果
     */
    suspend fun getOrderDetail(
        orderId: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建业务参数
                val businessParams = buildAppBusinessParams()
                businessParams["orderid"] = orderId
                businessParams["cashierswitch"] = "1"
                businessParams["abdata"] = "{\"order_detail_new_people_850\":\"A\"}"

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.ORDER_DETAIL_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建需要签名的Str
                val needSignStr = urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                    .replace("=", "").replace("&", "")
                    .replace(
                        "%7B%22order_detail_new_people_850%22%3A%22A%22%7D",
                        "{\"order_detail_new_people_850\":\"A\"}"
                    )

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "getOrderDetail: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getOrderDetail: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, false, false)

                requestHelper.get(fullUrl, headers)
            }

            "mini" -> {
                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["orderid"] = orderId
                businessParams["cashierswitch"] = "1"
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.ORDER_DETAIL_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    "",
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getOrderDetail: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.get(fullUrl, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 删除订单
     * @param orderId 订单ID
     * @return 删除订单响应结果
     */
    suspend fun orderDelete(
        orderId: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建业务参数
                val businessParams = buildAppBusinessParams()
                businessParams["orderid"] = orderId

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.ORDER_DELETE_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建需要签名的URL
                val needSignStr = urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                    .replace("=", "").replace("&", "")

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "orderDelete: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "orderDelete: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, false, false)

                requestHelper.get(fullUrl, headers)
            }

            "mini" -> {
                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["orderid"] = orderId
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.ORDER_DELETE_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    "",
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "orderDelete: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.get(fullUrl, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 取消订单
     * @param orderId 订单ID
     * @return 取消订单响应结果
     */
    suspend fun orderCancel(
        orderId: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建业务参数
                val businessParams = buildAppBusinessParams()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.ORDER_CANCEL_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody = """{"orderid":"$orderId","reason":"未用优惠券"}"""

                // 构建需要签名的URL
                val needSignUrl = urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                    .replace("=", "").replace("&", "") + requestBody

                // 生成签名
                val sign = requestHelper.generateSign(needSignUrl, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "orderCancel: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "orderCancel: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.ORDER_CANCEL_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                // 构建请求体
                val requestBody = """{"orderid":"$orderId","reason":"未用优惠券"}"""

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "orderCancel: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 订单预支付
     * @param orderId 订单ID
     * @param payType 支付类型
     * @return 订单预支付响应结果
     */
    suspend fun orderPrePay(
        orderId: String,
        payType: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建业务参数
                val businessParams = buildAppBusinessParams()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.ORDER_PRE_PAY_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody =
                    """{"bankCode":"","cardId":"","orderId":"$orderId","payType":"$payType","toolId":""}"""

                // 构建需要签名的URL
                val needSignUrl = urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                    .replace("=", "").replace("&", "") + requestBody

                // 生成签名
                val sign = requestHelper.generateSign(needSignUrl, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "orderPrePay: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "orderPrePay: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, false, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()
                // 必须设置openId，才能支付
                commonParams["openId"] = RequestConfig.MiniProgramVersion.OPEN_ID

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.ORDER_PRE_PAY_PATH,
                    businessParams,
                    commonParams,
                    "api"
                )

                // 构建请求体
                val requestBody =
                    """{"bankCode":"","cardId":"","orderId":"$orderId","payType":"$payType","toolId":""}"""

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "orderPrePay: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 检查订单红包
     * @param orderId 订单ID
     * @return 订单红包检查响应结果
     */
    suspend fun orderRedEnvelope(orderId: String): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    // 构建通用参数
                    val commonParams = requestHelper.getAppCommonParams()

                    // 构建业务参数
                    val businessParams = buildAppBusinessParams()
                    businessParams["orderid"] = orderId

                    // 构建URL
                    val urlWithParams = buildAppApiUrl(
                        RequestConfig.Path.ORDER_RED_ENVELOPE_PATH,
                        businessParams,
                        commonParams
                    )

                    // 构建需要签名的URL
                    val needSignStr =
                        urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                            .replace("=", "").replace("&", "")

                    // 生成签名
                    val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                    if (sign.isEmpty()) {
                        Log.e(tag, "orderRedEnvelope: sign empty, service may not be initialized")
                        return@withContext RequestResult.Error(Exception("服务未初始化"))
                    }
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "orderRedEnvelope: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildAppStandardHeaders(xyhBizParams, false, false)

                    requestHelper.get(fullUrl, headers)
                }

                "mini" -> {
                    // 构建通用参数
                    val commonParams = requestHelper.getMiniProgramCommonParams()

                    // 构建业务参数
                    val businessParams = buildMiniProgramBusinessParams()
                    businessParams["orderid"] = orderId
                    businessParams["memberid"] = requestHelper.getUid()
                    businessParams["distinctId"] = requestHelper.getUid()

                    // 构建URL
                    val urlWithParams = buildMiniApiUrl(
                        RequestConfig.Path.ORDER_RED_ENVELOPE_PATH_MINI,
                        businessParams,
                        commonParams,
                        "activity"
                    )

                    // 生成签名
                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        "",
                        SignType.MINIPROGRAM.value,
                        requestHelper.getUid()
                    )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "orderRedEnvelope: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildMiniStandardHeaders(xyhBizParams, false)

                    requestHelper.get(fullUrl, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }

    /**
     * 检查是否可以更新配送信息
     * @param orderId 订单ID
     * @return 检查结果响应
     */
    suspend fun canUpdateDeliveryInfo(orderId: String): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    // 构建通用参数
                    val commonParams = requestHelper.getAppCommonParams()

                    // 构建业务参数
                    val businessParams = buildAppBusinessParams()
                    businessParams["orderId"] = orderId

                    // 构建URL
                    val urlWithParams = buildAppApiUrl(
                        RequestConfig.Path.CAN_UPDATE_DELIVERY_INFO_PATH,
                        businessParams,
                        commonParams
                    )

                    // 构建需要签名的URL
                    val needSignStr =
                        urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                            .replace("=", "").replace("&", "")

                    // 生成签名
                    val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                    if (sign.isEmpty()) {
                        Log.e(
                            tag,
                            "canUpdateDeliveryInfo: sign empty, service may not be initialized"
                        )
                        return@withContext RequestResult.Error(Exception("服务未初始化"))
                    }
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "canUpdateDeliveryInfo: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildAppStandardHeaders(xyhBizParams, false, false)

                    requestHelper.get(fullUrl, headers)
                }

                "mini" -> {
                    // 构建通用参数
                    val commonParams = requestHelper.getMiniProgramCommonParams()

                    // 构建业务参数
                    val businessParams = buildMiniProgramBusinessParams()
                    businessParams["orderId"] = orderId
                    businessParams["memberid"] = requestHelper.getUid()
                    businessParams["distinctId"] = requestHelper.getUid()

                    // 构建URL
                    val urlWithParams = buildMiniApiUrl(
                        RequestConfig.Path.CAN_UPDATE_DELIVERY_INFO_PATH,
                        businessParams,
                        commonParams,
                        "activity"
                    )

                    // 生成签名
                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        "",
                        SignType.MINIPROGRAM.value,
                        requestHelper.getUid()
                    )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "canUpdateDeliveryInfo: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildMiniStandardHeaders(xyhBizParams, false)

                    requestHelper.get(fullUrl, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }

    /**
     * 获取订单配送信息
     * @param orderId 订单ID
     * @param bodyStr 请求体字符串
     * @return 订单配送信息响应结果
     */
    suspend fun getOrderDeliveryInfo(
        orderId: String,
        bodyStr: String = ""
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建业务参数
                val businessParams = buildAppBusinessParams()
                businessParams["orderid"] = orderId

                // 构建URL
                val urlWithParams = buildAppApiUrl(
                    RequestConfig.Path.GET_ORDER_DELIVERY_INFO_PATH,
                    businessParams,
                    commonParams
                )

                // 构建请求体
                val requestBody = if (bodyStr.isEmpty()) "{}" else "{$bodyStr}"

                // 构建需要签名的URL
                val needSignUrl = urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                    .replace("=", "").replace("&", "") + requestBody

                // 生成签名
                val sign = requestHelper.generateSign(needSignUrl, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "getOrderDeliveryInfo: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getOrderDeliveryInfo: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, false, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["orderid"] = orderId
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.GET_ORDER_DELIVERY_INFO_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                // 构建请求体
                val requestBody = if (bodyStr.isEmpty()) "{}" else "{$bodyStr}"

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getOrderDeliveryInfo: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 更新配送信息
     * @param modifyReturnParamJSONObject 修改的配送信息JSON对象
     * @return 更新配送信息响应结果
     */
    suspend fun updateDeliveryInfo(
        modifyReturnParamJSONObject: JSONObject
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建业务参数
                val businessParams = buildAppBusinessParams()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.UPDATE_DELIVERY_INFO_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody = modifyReturnParamJSONObject.toString()

                // 构建需要签名的URL
                val needSignStr = urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                    .replace("=", "").replace("&", "") + requestBody

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "updateDeliveryInfo: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "updateDeliveryInfo: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, false, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.UPDATE_DELIVERY_INFO_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    modifyReturnParamJSONObject.toString(),
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "updateDeliveryInfo: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.postJson(fullUrl, modifyReturnParamJSONObject.toString(), headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    suspend fun invoiceCanApplyOrderList(page: Int = 0, size: Int = 10): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    if (sellerId.isEmpty() || shopId.isEmpty()) {
                        Log.e(tag, "invoiceCanApplyOrderList: sellerId or shopId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                    }

                    val commonParams = requestHelper.getAppCommonParams()
                    val businessParams = buildAppWebBusinessParams()
                    businessParams["page"] = page
                    businessParams["size"] = size
                    businessParams["sellerid"] = sellerId
                    businessParams["shopid"] = shopId
                    businessParams["memberid"] = requestHelper.getUid()

                    val urlWithParams = buildAppApiUrl(
                        RequestConfig.Path.INVOICE_CAN_APPLY_ORDER_LIST_PATH,
                        businessParams,
                        commonParams
                    )

                    val sign =
                        requestHelper.generateSign(urlWithParams, "", SignType.APP_WEB.value)
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getWebXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "checkCardBuy: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildAppStandardHeaders(xyhBizParams, true, false)

                    requestHelper.get(fullUrl, headers)
                }

                "mini" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    if (sellerId.isEmpty() || shopId.isEmpty()) {
                        Log.e(tag, "invoiceCanApplyOrderList: sellerId or shopId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                    }

                    val commonParams = requestHelper.getMiniProgramCommonParams()
                    val businessParams = buildMiniProgramWebBusinessParams()
                    businessParams["page"] = page.toString()
                    businessParams["size"] = size.toString()
                    businessParams["sellerid"] = sellerId
                    businessParams["shopid"] = shopId
                    businessParams["memberid"] = requestHelper.getUid()

                    val urlWithParams = buildMiniApiUrl(
                        RequestConfig.Path.INVOICE_CAN_APPLY_ORDER_LIST_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        "",
                        SignType.MINIPROGRAM_WEB_INFO.value
                    )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getWebXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "invoiceCanApplyOrderList: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildMiniStandardHeaders(xyhBizParams, true)

                    requestHelper.get(fullUrl, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }

    suspend fun afterSalesList(page: Int = 0, size: Int = 10): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    if (sellerId.isEmpty() || shopId.isEmpty()) {
                        Log.e(tag, "invoiceCanApplyOrderList: sellerId or shopId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                    }

                    val commonParams = requestHelper.getAppCommonParams()
                    val businessParams = buildAppBusinessParams()
                    businessParams["page"] = page
                    businessParams["size"] = size
                    businessParams["sellerid"] = sellerId
                    businessParams["shopid"] = shopId

                    val urlWithParams = buildAppApiUrl(
                        RequestConfig.Path.AFTER_SALES_LIST_PATH,
                        businessParams,
                        commonParams
                    )

                    val needSignUrl =
                        urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                            .replace("=", "").replace("&", "")

                    val sign = requestHelper.generateSign(needSignUrl, "", SignType.APP.value)
                    if (sign.isEmpty()) {
                        Log.e(
                            tag,
                            "getOrderDeliveryInfo: sign empty, service may not be initialized"
                        )
                        return@withContext RequestResult.Error(Exception("服务未初始化"))
                    }
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "getOrderDeliveryInfo: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildAppStandardHeaders(xyhBizParams, false, false)

                    requestHelper.get(fullUrl, headers)
                }

                "mini" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    if (sellerId.isEmpty() || shopId.isEmpty()) {
                        Log.e(tag, "afterSalesList: sellerId or shopId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                    }

                    val commonParams = requestHelper.getMiniProgramCommonParams()
                    val businessParams = buildMiniProgramBusinessParams()
                    businessParams["page"] = page.toString()
                    businessParams["size"] = size.toString()
                    businessParams["sellerid"] = sellerId
                    businessParams["shopid"] = shopId
                    businessParams["memberid"] = requestHelper.getUid()
                    businessParams["distinctId"] = requestHelper.getUid()

                    val urlWithParams = buildMiniApiUrl(
                        RequestConfig.Path.AFTER_SALES_LIST_PATH,
                        businessParams,
                        commonParams,
                        "activity"
                    )

                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        "",
                        SignType.MINIPROGRAM.value,
                        requestHelper.getUid()
                    )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "afterSalesList: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildMiniStandardHeaders(xyhBizParams, false)

                    requestHelper.get(fullUrl, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }

    /**
     * 订单售后申请退款
     * @param orderId 订单ID
     * @return 售后申请响应结果
     */
    suspend fun orderAfterSales(
        orderId: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "orderAfterSales: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.AFTER_SALES_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody = """{"orderid":"$orderId","reason":"return-3-2","comment":""}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "orderAfterSales: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)
                headers["Content-Type"] = "application/json"

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "orderAfterSales: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.AFTER_SALES_PATH,
                    businessParams,
                    commonParams,
                    "api"
                )

                // 构建请求体
                val requestBody = """{"orderid":"$orderId","reason":"return-3-2","comment":""}"""

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM_WEB_INFO.value
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "orderAfterSales: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 下单接口
     * @param selectedAddress 选择的地址
     * @param selectedCart 选择的购物车
     * @param firstOrderPlace 是否首次提交下单请求
     * @param selectedCouponCodeArr 选择的优惠券
     * @param selectedRedPacketCodeArr 选择的红包
     * @param balancepayoption 永辉卡支付选项
     * @param ispickself 是否自提
     * @param pointpayoption 积分支付选项
     * @return 下单响应结果
     */
    suspend fun orderPlace(
        selectedAddress: AddressItem,
        selectedCart: CartItem,
        firstOrderPlace: Boolean = false,
        selectedCouponCodeArr: String = "",
        selectedRedPacketCodeArr: String = "",
        balancepayoption: String = "",
        pointpayoption: String = "",
        ispickself: String = "",
        onRefreshOrderPlaceParams: (url: String, orderPlaceBody: JSONObject) -> Unit = { _, _ -> }
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                // 获取XYHBizParams
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "orderPlace: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                // 获取请求参数
                val requestParams = orderPlaceRequestParams(
                    selectedAddress,
                    selectedCart,
                    xyhBizParams,
                    firstOrderPlace,
                    selectedCouponCodeArr,
                    selectedRedPacketCodeArr,
                    balancepayoption,
                    ispickself,
                    pointpayoption
                )

                // 获取URL和请求体
                val url = requestParams["url"] as String
                val needSignStr = requestParams["needSignStr"] as String
                val body = requestParams["body"] as String
                val headers = requestParams["header"] as Map<String, String>

                // 保存最新orderPlaceBody
                onRefreshOrderPlaceParams(url, JSONObject(body))

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "orderPlace: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$url&sign=$sign"

                // 发送请求
                requestHelper.postJson(fullUrl, body, headers)
            }

            "mini" -> {
                // 获取XYHBizParams
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "orderPlace: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                // 获取请求参数
                val requestParams = orderPlaceRequestParams(
                    selectedAddress,
                    selectedCart,
                    xyhBizParams,
                    firstOrderPlace,
                    selectedCouponCodeArr,
                    selectedRedPacketCodeArr,
                    balancepayoption,
                    ispickself,
                    pointpayoption
                )

                // 获取URL和请求体
                val url = requestParams["url"] as String
                val body = requestParams["body"] as String
                val headers = requestParams["header"] as Map<String, String>

                // 保存最新orderPlaceBody
                onRefreshOrderPlaceParams(url, JSONObject(body))

                // 生成签名
                val sign = requestHelper.generateSign(
                    url,
                    body,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$url&sign=$sign"

                // 发送请求
                requestHelper.postJson(fullUrl, body, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 构建下单请求参数
     * @param selectedAddress 选择的地址
     * @param selectedCart 选择的购物车
     * @param firstOrderPlace 是否首次提交下单请求
     * @param selectedCouponCodeArr 选择的优惠券
     * @param selectedRedPacketCodeArr 选择的红包
     * @param balancepayoption 永辉卡支付选项
     * @param ispickself 是否自提
     * @param pointpayoption 积分支付选项
     * @return 请求参数Map
     */
    private fun orderPlaceRequestParams(
        selectedAddress: AddressItem,
        selectedCart: CartItem,
        xyhBizParams: String,
        firstOrderPlace: Boolean = false,
        selectedCouponCodeArr: String = "",
        selectedRedPacketCodeArr: String = "",
        balancepayoption: String = "",
        ispickself: String = "",
        pointpayoption: String = "",
    ): Map<String, Any> {
        // 构建通用参数
        val commonParams =
            if (serviceType == "app") requestHelper.getAppCommonParams() else requestHelper.getMiniProgramCommonParams()

        // 构建业务参数
        val businessParams =
            if (serviceType == "app") buildAppBusinessParams() else buildMiniProgramBusinessParams()
        businessParams["abdata"] =
            "{\"order_fangMenKouT\":\"1\",\"yh_orderHuanGoupin\":\"1\",\"order_place_address_rec\":\"1\",\"app_order_relatedAdd_v10410\":\"a\",\"yh_placaXrnews\":\"1\",\"coupon_couponbag_1040\":\"b\",\"yh_orderTuiJianPing\":\"1\",\"placequanbao\":\"1\",\"yh_jskjzf\":\"1\"}"
        businessParams["distinctId"] = requestHelper.getUid()

        // 构建URL
        val urlWithParams =
            if (serviceType == "app") buildAppApiUrl(
                RequestConfig.Path.ORDER_PLACE_PATH,
                businessParams,
                commonParams
            ) else buildMiniApiUrl(
                RequestConfig.Path.ORDER_PLACE_PATH,
                businessParams,
                commonParams,
                "activity"
            )

        // 提取地址信息
        val address = selectedAddress.address
        val gender = selectedAddress.gender
        val id = selectedAddress.id
        val isdefault = selectedAddress.isdefault
        val location = selectedAddress.location
        val name = selectedAddress.name
        val nextdaydeliver = selectedAddress.nextdaydeliver
        val phone = selectedAddress.phone
        val scope = "0"

        val district = requestHelper.getDistrict()

        val area = address.area
        val city = address.city
        val cityid = address.cityid
        val detail = address.detail

        val lat = location?.lat
        val lng = location?.lng

        // 提取购物车信息
        val cartType = selectedCart.cartType
        val additionalBuyCacheKey = selectedCart.additionalBuyCacheKey
        val realshopid = selectedCart.realshopid
        val storeid = selectedCart.storeid
        val sellerid = selectedCart.seller?.id

        // 处理商品列表
        val products = JSONArray()
        val riskOrderSkuList = StringBuilder()
        val currentCartModels = selectedCart.cartModels

        for (cartModel in currentCartModels) {
            if (cartModel.modelType == 5) {
                val data = cartModel.data as Map<*, *>

                val selectstate = (data["selectstate"]?.toString()?.toFloatOrNull() ?: 0f).toInt()
                if (selectstate != 1) {
                    continue
                }

                val productJson = JSONObject()

                // 添加可选字段
                data["bundlepromocode"]?.toString()?.let {
                    productJson.put("bundlepromocode", it)
                }

                data["orderremark"]?.toString()?.let {
                    productJson.put(
                        "orderremark", try {
                            JSONArray(it)
                        } catch (e: Exception) {
                            it
                        }
                    )
                }

                data["productRemarks"]?.toString()?.let {
                    productJson.put("productRemarks", JSONArray(it))
                }

                val goodstagid = (data["goodstagid"]?.toString()?.toFloatOrNull() ?: 0f).toInt()
                val goodId = data["id"]?.toString() ?: continue
                val num = data["num"]?.toString()?.toFloatOrNull()?.toInt() ?: continue

                // 构建商品信息
                productJson.put("goodstagid", goodstagid)
                productJson.put("id", goodId)
                productJson.put("isbulkitem", 0)
                productJson.put("isspu", 0)
                productJson.put("multibuy", 0)
                productJson.put("num", num)
                productJson.put("pattern", "t")
                productJson.put("pseudofresh", 0)
                productJson.put("splitRowNum", 0)

                products.put(productJson)

                // 累加商品ID列表
                if (riskOrderSkuList.isNotEmpty()) {
                    riskOrderSkuList.append(",")
                }
                riskOrderSkuList.append(goodId)
            }
        }

        // 处理各种选项值，确保类型一致性
        val selectcouponactionUsed =
            if (selectedCouponCodeArr.isNotEmpty() || selectedRedPacketCodeArr.isNotEmpty()) "1" else "0"
        val balancepayoptionUsed = balancepayoption.ifEmpty { "1" }
        val ispickselfUsed = ispickself.ifEmpty { "0" }
        val pointpayoptionUsed = pointpayoption.ifEmpty { "0" }
        val packingbagoptionUsed = if (ispickselfUsed == "0") "1" else "0"
        val autocoupon = if (firstOrderPlace) "1" else "0"

        // 构建请求体JSON对象
        val bodyJson = JSONObject()

        // 处理additionalBuy相关选项
        if (firstOrderPlace) {
            bodyJson.put("additionalBuyCacheKey", additionalBuyCacheKey)
        } else {
            bodyJson.put("additionalBuyProductList", JSONArray())
        }

        // 添加通用参数
        bodyJson.put("autoPickupCoupon", 1)
        bodyJson.put("autocoupon", autocoupon.toInt())
        bodyJson.put("balancepayoption", balancepayoptionUsed.toInt())
        bodyJson.put("cartType", cartType)
        bodyJson.put("cashierswitch", 1)
        bodyJson.put("cateCardPayAmt", 0)
        bodyJson.put("chooseCouponPackage", 0)
        bodyJson.put("day", -1)
        // 免运券使用
        bodyJson.put("freedeliveryoption", 1)
        bodyJson.put("hasCheck", "0")
        bodyJson.put("jysessionid", requestHelper.jysessionid)
        bodyJson.put("mobile", phone)
        bodyJson.put("month", -1)
        bodyJson.put("obtainedCouponPackage", 0)
        bodyJson.put("operateElement", 0)
        bodyJson.put("packingbagoption", packingbagoptionUsed.toInt())
        bodyJson.put("pickself", ispickselfUsed.toInt())
        bodyJson.put("pointpayoption", pointpayoptionUsed.toInt())
        bodyJson.put("products", products)
        bodyJson.put("quickPaymentOption", 1)
        bodyJson.put("realshopid", realshopid)
        bodyJson.put("rechargeoption", 0)

        // 构建地址信息
        val recvinfoJson = JSONObject()
        val addressJson = JSONObject()
        addressJson.put("area", area)
        addressJson.put("city", city)
        addressJson.put("cityid", cityid)
        addressJson.put("detail", detail)
        addressJson.put("district", district)
        addressJson.put("_uuid", UUID.randomUUID().toString())

        val locationJson = JSONObject()
        locationJson.put("lat", lat)
        locationJson.put("lng", lng)
        locationJson.put("_uuid", UUID.randomUUID().toString())

        recvinfoJson.put("address", addressJson)
        recvinfoJson.put("district", district)
        recvinfoJson.put("foodsupport", 0)
        recvinfoJson.put("gender", gender)
        recvinfoJson.put("id", id)
        recvinfoJson.put("isSearch", false)
        recvinfoJson.put("ischanged", false)
        recvinfoJson.put("isdefault", isdefault)
        recvinfoJson.put("itemType", 0)
        recvinfoJson.put("location", locationJson)
        recvinfoJson.put("name", name)
        recvinfoJson.put("nextdaydeliver", nextdaydeliver)
        recvinfoJson.put("phone", phone)
        recvinfoJson.put("scope", scope.toInt())
        recvinfoJson.put("_uuid", UUID.randomUUID().toString())

        bodyJson.put("recvinfo", recvinfoJson)

        // 处理风控参数
        val riskCouponId =
            if (selectcouponactionUsed == "1") selectedCouponCodeArr.replace("\"", "") else ""
        bodyJson.put("riskCouponId", riskCouponId)
        bodyJson.put("riskCpuBuild", "armeabi-v7a")
        bodyJson.put("riskLoginType", "3")
        bodyJson.put("riskOperator", "联通")
        bodyJson.put("riskOrderPayValue", "0")
        bodyJson.put("riskOrderSkuList", riskOrderSkuList.toString())
        bodyJson.put("riskPayType", "")
        bodyJson.put("riskPhonePower", (1..100).random().toString())
        bodyJson.put("riskReceiveAddress", area + detail)
        bodyJson.put("riskReceiveArea", id)
        bodyJson.put("riskReceiveCity", id)
        bodyJson.put("riskReceiverName", name)
        bodyJson.put("riskReceiverPhone", phone)
        bodyJson.put("riskScene", "3")
        bodyJson.put("selectcouponaction", selectcouponactionUsed.toInt())


        // TODO: 未处理参数
        //bodyJson.put("selectedPickupCoupons", JSONArray())
        //addressCheck、employeeMember、lastpaytype、militaryMember、latitude、longitude、needFreeGoods

        // 处理可选的优惠券和红包
        if (selectedCouponCodeArr.isNotEmpty()) {
            bodyJson.put("selectedcoupons", JSONArray(selectedCouponCodeArr))
        }

        if (selectedRedPacketCodeArr.isNotEmpty()) {
            bodyJson.put("selectedredpackets", JSONArray(selectedRedPacketCodeArr))
        }

        bodyJson.put("selfMentionCode", "")
        bodyJson.put("sellerid", sellerid)
        bodyJson.put("source", 0)
        bodyJson.put("storeid", storeid)
        bodyJson.put("svipcardoption", 0)
        bodyJson.put("type", cartType)
        bodyJson.put("uid", requestHelper.getUid())
        bodyJson.put("cid", requestHelper.getChannel())
        bodyJson.put("mid", "AppChannel")
        bodyJson.put("sid", "AndroidStore")

        // 将JSON对象转换为字符串
        val body = bodyJson.toString()

        // 构建签名字符串
        val needSignStr = (urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
            .replace("=", "").replace("&", "") + body).replace(
            "%7B%22order_fangMenKouT%22%3A%221%22%2C%22yh_orderHuanGoupin%22%3A%221%22%2C%22order_place_address_rec%22%3A%221%22%2C%22app_order_relatedAdd_v10410%22%3A%22a%22%2C%22yh_placaXrnews%22%3A%221%22%2C%22coupon_couponbag_1040%22%3A%22b%22%2C%22yh_orderTuiJianPing%22%3A%221%22%2C%22placequanbao%22%3A%221%22%2C%22yh_jskjzf%22%3A%221%22%7D",
            "{\"order_fangMenKouT\":\"1\",\"yh_orderHuanGoupin\":\"1\",\"order_place_address_rec\":\"1\",\"app_order_relatedAdd_v10410\":\"a\",\"yh_placaXrnews\":\"1\",\"coupon_couponbag_1040\":\"b\",\"yh_orderTuiJianPing\":\"1\",\"placequanbao\":\"1\",\"yh_jskjzf\":\"1\"}"
        )

        Log.d("OrderService", "needSignStr: $needSignStr")

        val header = if (serviceType == "app") buildAppStandardHeaders(
            xyhBizParams,
            false,
            true
        ) else buildMiniStandardHeaders(xyhBizParams, false)

        return mapOf(
            "httpMethod" to "POST",
            "url" to urlWithParams,
            "needSignStr" to needSignStr,
            "header" to header,
            "body" to body
        )
    }

    /**
     * 订单确认接口
     * @param orderPlaceData 下单返回的数据
     * @param comment 订单备注
     * @param texpecttime 期望送达时间
     * @return 订单确认响应结果
     */
    suspend fun orderConfirm(
        orderPlaceData: OrderPlaceData,
        comment: String = "",
        texpecttime: String = ""
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                // 获取XYHBizParams
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "orderConfirm: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                // 获取orderPlaceUrl
                val orderPlaceUrl = requestHelper.getOrderPlaceUrl()

                // 获取orderPlaceBody
                val orderPlaceBody = requestHelper.getOrderPlaceBody()

                // 获取请求参数
                val requestParams = orderConfirmRequestParams(
                    xyhBizParams,
                    orderPlaceUrl,
                    orderPlaceData,
                    orderPlaceBody,
                    comment,
                    texpecttime
                )

                // 获取URL和请求体
                val url = requestParams["url"] as String
                val needSignStr = requestParams["needSignStr"] as String
                val body = requestParams["body"] as String
                val headers = requestParams["header"] as Map<String, String>

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "orderConfirm: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$url&sign=$sign"

                // 发送请求
                requestHelper.postJson(fullUrl, body, headers)
            }

            "mini" -> {
                // 获取XYHBizParams
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "orderConfirm: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                // 获取orderPlaceUrl
                val orderPlaceUrl = requestHelper.getOrderPlaceUrl()

                // 获取orderPlaceBody
                val orderPlaceBody = requestHelper.getOrderPlaceBody()

                // 获取请求参数
                val requestParams = orderConfirmRequestParams(
                    xyhBizParams,
                    orderPlaceUrl,
                    orderPlaceData,
                    orderPlaceBody,
                    comment,
                    texpecttime
                )

                // 获取URL和请求体
                val url = requestParams["url"] as String
                val body = requestParams["body"] as String
                val headers = requestParams["header"] as Map<String, String>

                // 生成签名
                val sign = requestHelper.generateSign(
                    url,
                    body,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$url&sign=$sign"

                // 发送请求
                requestHelper.postJson(fullUrl, body, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 构建订单确认请求参数
     * @param xyhBizParams XYHBizParams通用参数
     * @param orderPlaceUrl 下单URL
     * @param orderPlaceData 下单返回的数据
     * @param orderPlaceBody 下单请求体
     * @param comment 订单备注
     * @param texpecttime 期望送达时间
     * @return 请求参数Map
     */
    private fun orderConfirmRequestParams(
        xyhBizParams: String,
        orderPlaceUrl: String = "",
        orderPlaceData: OrderPlaceData,
        orderPlaceBody: JSONObject,
        comment: String = "",
        texpecttime: String = "", //{"date":1727193600000,"timeslots":[{"displayMode":0,"immediatedesc":"尽快送达，预计09:35送达","slottype":"immediate"}]}
    ): Map<String, Any> {

        // 构建通用参数
        val commonParams =
            if (serviceType == "app") requestHelper.getAppCommonParams() else requestHelper.getMiniProgramCommonParams()

        // 构建业务参数
        val businessParams =
            if (serviceType == "app") buildAppBusinessParams() else buildMiniProgramBusinessParams()
        businessParams["distinctId"] = requestHelper.getUid()

        // 构建URL
        val urlWithParams =
            if (serviceType == "app") buildAppApiUrl(
                RequestConfig.Path.ORDER_CONFIRM_PATH,
                businessParams,
                commonParams
            ) else buildMiniApiUrl(
                RequestConfig.Path.ORDER_CONFIRM_PATH,
                businessParams,
                commonParams,
                "activity"
            )

        //orderPlaceBody
        val cashierswitch = orderPlaceBody.getInt("cashierswitch")
        val cateCardPayAmt = orderPlaceBody.getString("cateCardPayAmt")
        val chooseCouponPackage = orderPlaceBody.getInt("chooseCouponPackage")
        val freedeliveryoption = orderPlaceBody.getInt("freedeliveryoption")
        val mid = orderPlaceBody.getString("mid")
        val mobile = orderPlaceBody.getString("mobile")
        val productsStr = orderPlaceBody.getJSONArray("products").toString()
        val packingbagoption = orderPlaceBody.getInt("packingbagoption")
        val pickself = orderPlaceBody.getInt("pickself")

        val recvinfo = orderPlaceBody.getJSONObject("recvinfo")
        var recvinfoStr = recvinfo.toString()

        val riskOrderSkuList = orderPlaceBody.getString("riskOrderSkuList")
        val riskPhonePower = orderPlaceBody.getString("riskPhonePower")
        val riskReceiveArea = orderPlaceBody.getString("riskReceiveArea")
        val riskReceiveCity = orderPlaceBody.getString("riskReceiveCity")
        val riskReceiverName = orderPlaceBody.getString("riskReceiverName")
        val riskReceiverPhone = orderPlaceBody.getString("riskReceiverPhone")
        val riskReceiveAddress = orderPlaceBody.getString("riskReceiveAddress")
        val sellerid = orderPlaceBody.getString("sellerid")
        val storeid = orderPlaceBody.getString("storeid")
        val sid = orderPlaceBody.getString("sid")
        val svipcardoption = orderPlaceBody.getInt("svipcardoption")
        val type = orderPlaceBody.getString("type")
        val pointpayoption = orderPlaceBody.getInt("pointpayoption")
        val rechargeoption = orderPlaceBody.getInt("rechargeoption")

        var selectedcouponsnew = if (orderPlaceBody.has("selectedcoupons")) {
            val selectedcoupons = orderPlaceBody.getJSONArray("selectedcoupons")
            ""","selectedcouponsnew":[{"couponCode":"${selectedcoupons[0]}"}]"""
        } else {
            ""
        }
        var selectedredpackets = if (orderPlaceBody.has("selectedredpackets")) {
            val selectedredpackets = orderPlaceBody.getJSONArray("selectedredpackets")
            ""","selectedredpackets":["${selectedredpackets[0]}"]"""
        } else {
            ""","selectedredpackets":[]"""
        }

        //orderPlaceData
        val balancepayoption = orderPlaceData.balancepayoption
        val placeorderid = orderPlaceData.placeorderid
        val placeKey = orderPlaceData.placeKey
        val totalpayment = orderPlaceData.totalpayment
        val orderamount = orderPlaceData.orderamount
        val widgets = orderPlaceData.widgets

        val riskPayType = if (totalpayment == 0) {
            "pay.yonghui.balance"
        } else {
            "pay.weixin.app"
        }

        val priceInfo = WidgetHelper.getPriceInfoGroupData(orderPlaceData)
        val couponsWidgetData =
            WidgetHelper.getSubWidgetDataFromGroup<CouponsWidgetData>(priceInfo, "coupons")
        if (couponsWidgetData != null) {
            val selectedcoupons = couponsWidgetData.selectedcoupons
            if (selectedcoupons != null) {
                selectedcouponsnew =
                    ""","selectedcouponsnew":[{"couponCode":"${selectedcoupons[0]}"}]"""
            }
        }
        val redPacketWidgetData =
            WidgetHelper.getSubWidgetDataFromGroup<RedPacketWidgetData>(priceInfo, "redpacket")
        if (redPacketWidgetData != null) {
            val selectedredpacketsTemp = redPacketWidgetData.selectedredpackets
            if (selectedredpacketsTemp != null) {
                selectedredpackets =
                    ""","selectedredpackets":["${selectedredpacketsTemp[0]}"]"""
            }
        }

        // 缺货处理
        val stockLackRemarkComment = """"stockLackRemarkComment":"缺货商品退款，其他商品正常配送""""
        val stockLackRemarkId = """"stockLackRemarkId":"2""""

        val md5Body =
            """{"appdownloadchanel":"${requestHelper.getChannel()}","balancepayoption":$balancepayoption,"cashierswitch":$cashierswitch,"cateCardIds":[],"cateCardPayAmt":$cateCardPayAmt,"checkConfirmReject":1,"chooseCouponPackage":$chooseCouponPackage,"cid":"${requestHelper.getChannel()}","comment":"$comment","day":-1,"device_info":"${requestHelper.getDeviceId()}","freedeliveryoption":$freedeliveryoption,"isAgreeSelfLaw":"0","jysessionid":"${requestHelper.jysessionid}","mid":"$mid","militaryMember":0,"mobile":"","month":-1,"packagelist":[{"products":$productsStr,"texpecttime":$texpecttime}],"packingbagoption":$packingbagoption,"paypasswordtype":0,"pickself":$pickself,"placeKey":"$placeKey","placeorderid":"$placeorderid","pointpayoption":$pointpayoption,"pricetotal":$orderamount,"products":$productsStr,"rechargeoption":$rechargeoption,"recvinfo":$recvinfoStr,"riskCouponId":"","riskCpuBuild":"armeabi-v7a","riskOperator":"联通","riskOrderComments":"$comment","riskOrderPayValue":"$orderamount","riskOrderSkuList":"$riskOrderSkuList","riskPayType":"$riskPayType","riskPhonePower":"$riskPhonePower","riskReceiveAddress":"$riskReceiveAddress","riskReceiveArea":"$riskReceiveArea","riskReceiveCity":"$riskReceiveCity","riskReceiverName":"$riskReceiverName","riskReceiverPhone":"$riskReceiverPhone","riskScene":"4"$selectedcouponsnew$selectedredpackets,"sellerid":$sellerid,"sid":"$sid",$stockLackRemarkComment,$stockLackRemarkId,"storeid":"$storeid","svipcardoption":$svipcardoption,"texpecttime":$texpecttime,"totalpayment":$totalpayment,"type":"$type","uid":"${requestHelper.getUid()}"}"""

        val midMd5Confirm = EncryptionUtil.md5(urlWithParams + md5Body)
        val midMd5Place = EncryptionUtil.md5(orderPlaceUrl)
        val midMd5UUID = UUID.randomUUID().toString()
        val _mid = "/web/trade/order/place/785#$midMd5Confirm#$midMd5Place$midMd5UUID"
        val address = recvinfo.getJSONObject("address")
        address.remove("district")
        address.put("_mid", _mid)
        val location = recvinfo.getJSONObject("location")
        location.put("_mid", _mid)
        recvinfo.remove("district")
        recvinfo.put("address", address)
        recvinfo.put("location", location)
        recvinfo.put("_mid", _mid)
        recvinfoStr = recvinfo.toString()

        val body =
            """{"appdownloadchanel":"${requestHelper.getChannel()}","balancepayoption":$balancepayoption,"cashierswitch":$cashierswitch,"cateCardIds":[],"cateCardPayAmt":$cateCardPayAmt,"checkConfirmReject":1,"chooseCouponPackage":$chooseCouponPackage,"cid":"${requestHelper.getChannel()}","comment":"$comment","day":-1,"device_info":"${requestHelper.getDeviceId()}","freedeliveryoption":$freedeliveryoption,"isAgreeSelfLaw":"0","jysessionid":"${requestHelper.jysessionid}","mid":"$mid","militaryMember":0,"mobile":"$mobile","month":-1,"packagelist":[{"products":$productsStr,"texpecttime":$texpecttime}],"packingbagoption":$packingbagoption,"paypasswordtype":0,"pickself":$pickself,"placeKey":"$placeKey","placeorderid":"$placeorderid","pointpayoption":$pointpayoption,"pricetotal":$orderamount,"products":$productsStr,"rechargeoption":$rechargeoption,"recvinfo":$recvinfoStr,"riskCouponId":"","riskCpuBuild":"armeabi-v7a","riskOperator":"联通","riskOrderComments":"$comment","riskOrderPayValue":"$orderamount","riskOrderSkuList":"$riskOrderSkuList","riskPayType":"$riskPayType","riskPhonePower":"$riskPhonePower","riskReceiveAddress":"$riskReceiveAddress","riskReceiveArea":"$riskReceiveArea","riskReceiveCity":"$riskReceiveCity","riskReceiverName":"$riskReceiverName","riskReceiverPhone":"$riskReceiverPhone","riskScene":"4"$selectedcouponsnew$selectedredpackets,"sellerid":$sellerid,"sid":"$sid",$stockLackRemarkComment,$stockLackRemarkId,"storeid":"$storeid","svipcardoption":$svipcardoption,"texpecttime":$texpecttime,"totalpayment":$totalpayment,"type":"$type","uid":"${requestHelper.getUid()}"}"""

        val needSignStr =
            urlWithParams.split("?")[1].split("&").sorted().joinToString("&").replace("=", "")
                .replace("&", "") + body

        Log.d("OrderService", "needSignStr: $needSignStr")

        val header = if (serviceType == "app") buildAppStandardHeaders(
            xyhBizParams,
            false,
            true
        ) else buildMiniStandardHeaders(xyhBizParams, false)

        return mapOf(
            "httpMethod" to "POST",
            "url" to urlWithParams,
            "needSignStr" to needSignStr,
            "header" to header,
            "body" to body
        )
    }
}