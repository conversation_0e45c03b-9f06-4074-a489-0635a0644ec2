package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.model.DeletedOrderEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 删除订单记录工具类
 * 提供便捷的删除订单记录查询和统计功能
 */
object DeletedOrderUtils {
    
    private const val TAG = "DeletedOrderUtils"
    
    /**
     * 获取所有删除订单记录
     */
    fun getAllDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        return database.deletedOrderDao().getAllDeletedOrders()
    }
    
    /**
     * 根据账号UID获取删除订单记录
     */
    fun getDeletedOrdersByUid(context: Context, uid: String): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        return database.deletedOrderDao().getDeletedOrdersByUid(uid)
    }
    
    /**
     * 根据手机号获取删除订单记录
     */
    fun getDeletedOrdersByPhoneNumber(context: Context, phoneNumber: String): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        return database.deletedOrderDao().getDeletedOrdersByPhoneNumber(phoneNumber)
    }
    
    /**
     * 获取今日删除的订单记录
     */
    fun getTodayDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfDay = calendar.timeInMillis
        
        calendar.add(Calendar.DAY_OF_MONTH, 1)
        val startOfNextDay = calendar.timeInMillis
        
        return database.deletedOrderDao().getDeletedOrdersByDateRange(startOfDay, startOfNextDay)
    }
    
    /**
     * 获取本周删除的订单记录
     */
    fun getThisWeekDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        val calendar = Calendar.getInstance()
        
        // 设置为本周第一天（周一）
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfWeek = calendar.timeInMillis
        
        // 设置为下周第一天
        calendar.add(Calendar.WEEK_OF_YEAR, 1)
        val startOfNextWeek = calendar.timeInMillis
        
        return database.deletedOrderDao().getDeletedOrdersByDateRange(startOfWeek, startOfNextWeek)
    }
    
    /**
     * 获取本月删除的订单记录
     */
    fun getThisMonthDeletedOrders(context: Context): Flow<List<DeletedOrderEntity>> {
        val database = AppDatabase.getDatabase(context)
        val calendar = Calendar.getInstance()
        
        // 设置为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfMonth = calendar.timeInMillis
        
        // 设置为下月第一天
        calendar.add(Calendar.MONTH, 1)
        val startOfNextMonth = calendar.timeInMillis
        
        return database.deletedOrderDao().getDeletedOrdersByDateRange(startOfMonth, startOfNextMonth)
    }
    
    /**
     * 检查订单是否已被删除
     */
    suspend fun isOrderDeleted(context: Context, orderId: String): Boolean {
        return try {
            val database = AppDatabase.getDatabase(context)
            database.deletedOrderDao().isOrderDeleted(orderId)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check if order is deleted: ${e.message}")
            false
        }
    }
    
    /**
     * 获取删除订单统计信息
     */
    suspend fun getDeletedOrderStatistics(context: Context): DeletedOrderStatistics {
        return try {
            val database = AppDatabase.getDatabase(context)
            val dao = database.deletedOrderDao()
            
            val totalCount = dao.getDeletedOrderCount()
            
            // 获取今日删除数量
            val calendar = Calendar.getInstance()
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfDay = calendar.timeInMillis
            calendar.add(Calendar.DAY_OF_MONTH, 1)
            val startOfNextDay = calendar.timeInMillis
            val todayCount = dao.getDeletedOrderCountByDateRange(startOfDay, startOfNextDay)
            
            // 获取本周删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfWeek = calendar.timeInMillis
            calendar.add(Calendar.WEEK_OF_YEAR, 1)
            val startOfNextWeek = calendar.timeInMillis
            val thisWeekCount = dao.getDeletedOrderCountByDateRange(startOfWeek, startOfNextWeek)
            
            // 获取本月删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_MONTH, 1)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfMonth = calendar.timeInMillis
            calendar.add(Calendar.MONTH, 1)
            val startOfNextMonth = calendar.timeInMillis
            val thisMonthCount = dao.getDeletedOrderCountByDateRange(startOfMonth, startOfNextMonth)
            
            DeletedOrderStatistics(
                totalCount = totalCount,
                todayCount = todayCount,
                thisWeekCount = thisWeekCount,
                thisMonthCount = thisMonthCount
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get deleted order statistics: ${e.message}")
            DeletedOrderStatistics()
        }
    }
    
    /**
     * 获取指定账号的删除订单统计信息
     */
    suspend fun getDeletedOrderStatisticsByUid(context: Context, uid: String): DeletedOrderStatistics {
        return try {
            val database = AppDatabase.getDatabase(context)
            val dao = database.deletedOrderDao()
            
            val totalCount = dao.getDeletedOrderCountByUid(uid)
            
            // 获取今日删除数量
            val calendar = Calendar.getInstance()
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfDay = calendar.timeInMillis
            calendar.add(Calendar.DAY_OF_MONTH, 1)
            val startOfNextDay = calendar.timeInMillis
            val todayRecords = dao.getDeletedOrdersByUidAndDateRange(uid, startOfDay, startOfNextDay).first()
            val todayCount = todayRecords.size
            
            // 获取本周删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfWeek = calendar.timeInMillis
            calendar.add(Calendar.WEEK_OF_YEAR, 1)
            val startOfNextWeek = calendar.timeInMillis
            val thisWeekRecords = dao.getDeletedOrdersByUidAndDateRange(uid, startOfWeek, startOfNextWeek).first()
            val thisWeekCount = thisWeekRecords.size
            
            // 获取本月删除数量
            calendar.time = Date()
            calendar.set(Calendar.DAY_OF_MONTH, 1)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfMonth = calendar.timeInMillis
            calendar.add(Calendar.MONTH, 1)
            val startOfNextMonth = calendar.timeInMillis
            val thisMonthRecords = dao.getDeletedOrdersByUidAndDateRange(uid, startOfMonth, startOfNextMonth).first()
            val thisMonthCount = thisMonthRecords.size
            
            DeletedOrderStatistics(
                totalCount = totalCount,
                todayCount = todayCount,
                thisWeekCount = thisWeekCount,
                thisMonthCount = thisMonthCount
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get deleted order statistics by uid: ${e.message}")
            DeletedOrderStatistics()
        }
    }
    
    /**
     * 格式化删除时间
     */
    fun formatDeleteTime(timestamp: Long): String {
        return try {
            val date = Date(timestamp)
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            formatter.format(date)
        } catch (e: Exception) {
            "时间格式错误"
        }
    }
    
    /**
     * 清理指定时间之前的删除记录（用于数据清理）
     */
    suspend fun cleanupOldRecords(context: Context, daysToKeep: Int = 30): Int {
        return try {
            val database = AppDatabase.getDatabase(context)
            val calendar = Calendar.getInstance()
            calendar.add(Calendar.DAY_OF_MONTH, -daysToKeep)
            val cutoffTime = calendar.timeInMillis

            database.deletedOrderDao().deleteDeletedOrdersBeforeTimestamp(cutoffTime)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup old records: ${e.message}")
            0
        }
    }

    /**
     * 备份所有已删除订单数据到文件
     *
     * @param context 上下文
     * @param subDirectory 子目录名称，默认为"BackupDeleteOrders"
     * @return 备份文件路径，如果备份失败则返回null
     */
    suspend fun backupDeletedOrdersToFile(
        context: Context,
        subDirectory: String = "BackupDeleteOrders"
    ): String? {
        return try {
            val database = AppDatabase.getDatabase(context)
            val deletedOrders = database.deletedOrderDao().getAllDeletedOrders().first()

            if (deletedOrders.isEmpty()) {
                Log.d(TAG, "No deleted orders to backup")
                return null
            }

            // 生成备份文件内容
            val backupContent = generateBackupContent(deletedOrders)

            // 生成文件名（包含时间戳）
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "deleted_orders_backup_$timestamp.csv"

            // 保存到公共下载目录的子目录
            val filePath = FileUtils.saveTextToPublicDownloads(
                context,
                fileName,
                backupContent,
                subDirectory
            )

            if (filePath != null) {
                Log.d(TAG, "Deleted orders backup saved to: $filePath")
                Log.d(TAG, "Backup contains ${deletedOrders.size} deleted order records")

                // 清理旧备份文件，只保留最新的5个
                try {
                    cleanupOldBackupFiles(context, subDirectory)
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to cleanup old backup files: ${e.message}", e)
                }
            } else {
                Log.e(TAG, "Failed to save deleted orders backup")
            }

            filePath
        } catch (e: Exception) {
            Log.e(TAG, "Failed to backup deleted orders: ${e.message}", e)
            null
        }
    }

    /**
     * 生成备份文件内容（CSV格式）
     */
    private fun generateBackupContent(deletedOrders: List<DeletedOrderEntity>): String {
        val sb = StringBuilder()

        // 添加CSV头部
        sb.appendLine("ID,订单ID,账号UID,手机号,订单标题,订单状态,删除时间戳,删除日期,备注")

        // 添加数据行
        for (order in deletedOrders) {
            sb.appendLine(
                "${order.id}," +
                "\"${order.orderId}\"," +
                "\"${order.uid}\"," +
                "\"${order.phoneNumber}\"," +
                "\"${order.orderTitle}\"," +
                "${order.orderStatus}," +
                "${order.deleteTimestamp}," +
                "\"${order.deleteDate}\"," +
                "\"${order.extraNote}\""
            )
        }

        return sb.toString()
    }

    /**
     * 检查文件内容是否为删除订单备份格式
     *
     * @param content 文件内容
     * @return 是否为删除订单备份格式
     */
    fun isDeletedOrderBackupFormat(content: String): Boolean {
        return try {
            val lines = content.trim().split("\n")
            if (lines.isEmpty()) return false

            // 检查第一行是否为CSV头部
            val header = lines[0].trim()
            header == "ID,订单ID,账号UID,手机号,订单标题,订单状态,删除时间戳,删除日期,备注"
        } catch (e: Exception) {
            Log.e(TAG, "Error checking deleted order backup format: ${e.message}")
            false
        }
    }

    /**
     * 从备份文件内容恢复删除订单数据
     *
     * @param context 上下文
     * @param content 备份文件内容
     * @return 恢复结果信息
     */
    suspend fun restoreDeletedOrdersFromBackup(
        context: Context,
        content: String
    ): RestoreResult {
        return try {
            val lines = content.trim().split("\n")
            if (lines.size < 2) {
                return RestoreResult(false, "备份文件格式错误：没有数据行")
            }

            // 跳过头部，解析数据行
            val dataLines = lines.drop(1)
            val deletedOrders = mutableListOf<DeletedOrderEntity>()
            var parseErrorCount = 0

            for ((index, line) in dataLines.withIndex()) {
                try {
                    val parsedOrder = parseDeletedOrderFromCsvLine(line.trim())
                    if (parsedOrder != null) {
                        deletedOrders.add(parsedOrder)
                    } else {
                        parseErrorCount++
                        Log.w(TAG, "Failed to parse line ${index + 2}: $line")
                    }
                } catch (e: Exception) {
                    parseErrorCount++
                    Log.e(TAG, "Error parsing line ${index + 2}: ${e.message}")
                }
            }

            if (deletedOrders.isEmpty()) {
                return RestoreResult(false, "有效的删除订单数据为空")
            }

            // 保存到数据库
            val database = AppDatabase.getDatabase(context)
            val insertedIds = database.deletedOrderDao().insertDeletedOrders(deletedOrders)

            val successMessage = "成功恢复${insertedIds.size}条删除订单记录" +
                    if (parseErrorCount > 0) "，跳过${parseErrorCount}条无效记录" else ""

            Log.d(TAG, successMessage)
            RestoreResult(true, successMessage)

        } catch (e: Exception) {
            val errorMessage = "恢复删除订单数据异常: ${e.message}"
            Log.e(TAG, errorMessage, e)
            RestoreResult(false, errorMessage)
        }
    }

    /**
     * 从CSV行解析DeletedOrderEntity
     *
     * @param csvLine CSV数据行
     * @return 解析出的DeletedOrderEntity或null
     */
    private fun parseDeletedOrderFromCsvLine(csvLine: String): DeletedOrderEntity? {
        return try {
            if (csvLine.isBlank()) return null

            // 简单的CSV解析，处理引号包围的字段
            val fields = parseCsvLine(csvLine)
            if (fields.size != 9) {
                Log.w(TAG, "CSV line has ${fields.size} fields, expected 9: $csvLine")
                return null
            }

            DeletedOrderEntity(
                id = 0, // 让数据库自动生成ID
                orderId = fields[1],
                uid = fields[2],
                phoneNumber = fields[3],
                orderTitle = fields[4],
                orderStatus = fields[5].toIntOrNull() ?: 0,
                deleteTimestamp = fields[6].toLongOrNull() ?: System.currentTimeMillis(),
                deleteDate = fields[7],
                extraNote = fields[8]
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing CSV line: $csvLine", e)
            null
        }
    }

    /**
     * 解析CSV行，处理引号包围的字段
     *
     * @param csvLine CSV行
     * @return 字段列表
     */
    private fun parseCsvLine(csvLine: String): List<String> {
        val fields = mutableListOf<String>()
        val currentField = StringBuilder()
        var inQuotes = false
        var i = 0

        while (i < csvLine.length) {
            val char = csvLine[i]

            when {
                char == '"' && !inQuotes -> {
                    inQuotes = true
                }
                char == '"' && inQuotes -> {
                    // 检查是否是转义的引号
                    if (i + 1 < csvLine.length && csvLine[i + 1] == '"') {
                        currentField.append('"')
                        i++ // 跳过下一个引号
                    } else {
                        inQuotes = false
                    }
                }
                char == ',' && !inQuotes -> {
                    fields.add(currentField.toString())
                    currentField.clear()
                }
                else -> {
                    currentField.append(char)
                }
            }
            i++
        }

        // 添加最后一个字段
        fields.add(currentField.toString())

        return fields
    }

    /**
     * 清理旧备份文件，只保留最新的5个备份文件
     *
     * @param context 上下文
     * @param subDirectory 子目录名称
     */
    private fun cleanupOldBackupFiles(context: Context, subDirectory: String) {
        try {
            // 获取备份目录
            val baseDownloadsDir = android.os.Environment.getExternalStoragePublicDirectory(
                android.os.Environment.DIRECTORY_DOWNLOADS
            ) ?: return

            val backupDir = if (subDirectory.isBlank()) {
                baseDownloadsDir
            } else {
                java.io.File(baseDownloadsDir, subDirectory)
            }

            if (!backupDir.exists()) {
                return
            }

            // 获取所有备份文件
            val backupFiles = backupDir.listFiles { file ->
                file.isFile && file.name.startsWith("deleted_orders_backup_") && file.name.endsWith(".csv")
            } ?: return

            // 按修改时间排序，最新的在前
            val sortedFiles = backupFiles.sortedByDescending { it.lastModified() }

            // 如果备份文件超过5个，删除多余的
            if (sortedFiles.size > 5) {
                val filesToDelete = sortedFiles.drop(5)
                var deletedCount = 0

                for (file in filesToDelete) {
                    try {
                        if (file.delete()) {
                            deletedCount++
                            Log.d(TAG, "Deleted old backup file: ${file.name}")
                        } else {
                            Log.w(TAG, "Failed to delete old backup file: ${file.name}")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error deleting old backup file: ${file.name}", e)
                    }
                }

                if (deletedCount > 0) {
                    Log.d(TAG, "Cleaned up $deletedCount old backup files, kept latest 5")
                }
            } else {
                Log.d(TAG, "Backup files count: ${sortedFiles.size}, no cleanup needed")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error during backup files cleanup: ${e.message}", e)
        }
    }
}

/**
 * 删除订单统计信息数据类
 */
data class DeletedOrderStatistics(
    val totalCount: Int = 0,
    val todayCount: Int = 0,
    val thisWeekCount: Int = 0,
    val thisMonthCount: Int = 0
)

/**
 * 恢复操作结果数据类
 */
data class RestoreResult(
    val success: Boolean,
    val message: String
)
